<template>
  <div class="branch-select-container">
    <q-select
      v-model="selectedBranch"
      :options="branchOptions"
      :loading="loading"
      :disable="loading"
      label="選擇分店"
      emit-value
      map-options
      option-value="value"
      option-label="label"
      clearable
      outlined
      class="q-mb-md"
      @update:model-value="onBranchChange"
    >
      <template v-slot:no-option>
        <q-item>
          <q-item-section class="text-grey">
            {{ error ? '載入分店失敗' : '沒有可用的分店' }}
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:loading>
        <q-item>
          <q-item-section>
            <q-item-label>載入中...</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <!-- 錯誤提示 -->
    <q-banner
      v-if="error"
      class="text-white bg-negative q-mb-md"
      rounded
    >
      <template v-slot:avatar>
        <q-icon name="error" />
      </template>
      {{ error }}
      <template v-slot:action>
        <q-btn
          flat
          color="white"
          label="重試"
          @click="retryFetch"
        />
      </template>
    </q-banner>

    <!-- 選中分店的詳細資訊 -->
    <q-card
      v-if="selectedBranchInfo"
      class="q-mt-md"
      flat
      bordered
    >
      <q-card-section>
        <div class="text-h6">{{ selectedBranchInfo.name }}</div>
        <div v-if="selectedBranchInfo.address" class="text-body2 text-grey-7">
          <q-icon name="place" class="q-mr-xs" />
          {{ selectedBranchInfo.address }}
        </div>
        <div v-if="selectedBranchInfo.phone" class="text-body2 text-grey-7">
          <q-icon name="phone" class="q-mr-xs" />
          {{ selectedBranchInfo.phone }}
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useBranches } from 'src/composables/useBranches';
import type { Branch } from 'src/types/api';

// Props
interface Props {
  modelValue?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
});

// Emits
interface Emits {
  (e: 'update:modelValue', value: number | null): void;
  (e: 'branchChange', branch: Branch | null): void;
}

const emit = defineEmits<Emits>();

// 使用分店 composable
const {
  branchOptions,
  loading,
  error,
  fetchBranches,
  getBranchById
} = useBranches();

// 本地狀態
const selectedBranch = ref<number | null>(props.modelValue);

// 計算屬性：選中分店的詳細資訊
const selectedBranchInfo = computed(() => {
  if (!selectedBranch.value) return null;
  return getBranchById(selectedBranch.value);
});

// 監聽 props 變化
watch(() => props.modelValue, (newValue) => {
  selectedBranch.value = newValue;
});

// 監聽選中分店變化
watch(selectedBranch, (newValue) => {
  emit('update:modelValue', newValue);
  const branchInfo = newValue ? getBranchById(newValue) : null;
  emit('branchChange', branchInfo);
});

// 分店變更處理
const onBranchChange = (value: number | null) => {
  selectedBranch.value = value;
};

// 重試獲取分店
const retryFetch = () => {
  fetchBranches();
};

// 組件掛載時獲取分店列表
onMounted(() => {
  fetchBranches();
});
</script>

<style scoped>
.branch-select-container {
  width: 100%;
  max-width: 400px;
}
</style>
